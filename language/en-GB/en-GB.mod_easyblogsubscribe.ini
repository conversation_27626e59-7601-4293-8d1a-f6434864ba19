; @package		EasyBlog
; @copyright	Copyright (C) 2010 - 2018 Stack Ideas Sdn Bhd. All rights reserved.
; @license		GNU/GPL, see LICENSE.php
; EasyBlog is free software. This version may have been modified pursuant
; to the GNU General Public License, and as distributed it includes or
; is derivative of works licensed under the GNU General Public License or
; other free or open source software licenses.

MOD_SUBSCRIBE_MESSAGE="Subscribe to our blog"
MOD_EASYBLOGSUBSCRIBE_YOUR_NAME="Your Name:"
MOD_EASYBLOGSUBSCRIBE_YOUR_EMAIL="Your Email:"
MOD_EASYBLOGSUBSCRIBE_SUBSCRIBE_BUTTON="Subscribe Now"
MOD_SUBSCRIBE_MESSAGE_SITE="Subscribe to our blog"
MOD_SUBSCRIBE_MESSAGE_CATEGORY="Subscribe to this category"
MOD_SUBSCRIBE_MESSAGE_TEAM="Subscribe to this team"
MOD_SUBSCRIBE_MESSAGE_BLOGGER="Subscribe to this blogger"
MOD_SUBSCRIBE_MESSAGE_ENTRY="Subscribe to this entry"
MOD_UNSUBSCRIBE_MESSAGE_SITE="Unsubscribe from our blog"
MOD_UNSUBSCRIBE_MESSAGE_CATEGORY="Unsubscribe this category"
MOD_UNSUBSCRIBE_MESSAGE_TEAM="Unsubscribe this team"
MOD_UNSUBSCRIBE_MESSAGE_BLOGGER="Unsubscribe this blogger"
MOD_UNSUBSCRIBE_MESSAGE_ENTRY="Unsubscribe this entry"
MOD_EASYBLOGSUBSCRIBE_YOUR_USERNAME="Username"
MOD_EASYBLOGSUBSCRIBE_REGISTER_AS_SITE_MEMBER="Automatically register me as a user of this site"
MOD_EB_SUBSCRIPTION_AUTO_DETECTION="Auto Detection Subscription Type"
MOD_EB_SUBSCRIPTION_AUTO_DETECTION_DESC="Turning this option on, it allows the module to auto detect subscription type based on current page or specified id. It will override the option defined below."