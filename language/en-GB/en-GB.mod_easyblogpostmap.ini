; @package      EasyBlog
; @copyright    Copyright (C) 2010 - 2015 Stack Ideas Sdn Bhd. All rights reserved.
; @license      GNU/GPL, see LICENSE.php
; EasyBlog is free software. This version may have been modified pursuant
; to the GNU General Public License, and as distributed it includes or
; is derivative of works licensed under the GNU General Public License or
; other free or open source software licenses.

; Tabs
COM_MODULES_POST_FIELDSET_LABEL="Post Options"
COM_MODULES_GOOGLEMAP_FIELDSET_LABEL="Google Maps"
COM_MODULES_OSM_FIELDSET_LABEL="OpenStreetMap"
COM_MODULES_RECENT_FIELDSET_LABEL="Filter Options"
COM_MODULES_AUTHOR_FIELDSET_LABEL="Filter Options"
COM_MODULES_CATEGORY_FIELDSET_LABEL="Filter Options"
COM_MODULES_TAGS_FIELDSET_LABEL="Filter Options"
COM_MODULES_TEAM_FIELDSET_LABEL="Filter Options"

; Listing
MOD_EASYBLOGPOSTMAP_POST_BY="By %1s"
MOD_EASYBLOGPOSTMAP_ADDRESS_AT="At %1s"
MOD_EASYBLOGPOSTMAP_HITS="Hits: %1s"
MOD_EASYBLOGPOSTMAP_TOTAL_COMMENTS="Comments: %1s"
MOD_EASYBLOGPOSTMAP_ERROR_LOADING_HEADERS="EasyBlog Post Map Error! Error loading headers."
MOD_EASYBLOGPOSTMAP_NO_LOCATION_POST_FOUND="No location tagged post found."
MOD_EASYBLOGPOSTMAP_RATEBLOG="Rate this blog entry"
