<style>
table{ border-collapse: separate !important; }
div#si-installer * { -webkit-box-sizing: border-box; -moz-box-sizing: border-box; box-sizing: border-box; font-family: Roboto,-apple-system,BlinkMacSystemFont,Segoe UI,Helvetica Neue,Arial,sans-serif;}

div#si-installer .clearfix,
div#si-installer .box-hd,
div#si-installer .box-bd { clear:none; display:block; }
div#si-installer .clearfix:after,
div#si-installer .box-hd,
div#si-installer .box-bd { content:"";display:table;clear:both; }
div#si-installer .box { background: #F9FBFF; border: 1px solid #F9FBFF; padding: 0px; margin-bottom: 20px; color: #777; border-radius: 3px;}
div#si-installer .box-hd,
div#si-installer .box-bd { width:100%; }

div#si-installer .box-bd { padding: 40px 16px !important; background: #F9FBFF; text-align: center;}
div#si-installer .box-bd > p { font-size: .85rem; line-height: 1.2rem; }
div#si-installer h1.si-title { font-size: 1.25rem; line-height: 1.2rem; color: #2c3e50; margin: 0 0 30px; }
div#si-installer .btn-install { display: inline-block; color: #FFF; font-size: 13px; font-weight: 700; padding: 10px 16px; background-color: #3E7AC0; border: 0; text-decoration: none; border-radius: 3px; }
div#si-installer .actions { margin-top: 30px; text-align: center !important; }

div#svg__ani {width: 280px; height: auto; margin: 0 auto;}
</style>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bodymovin/5.5.9/lottie.min.js"></script>

<div id="si-installer">
	<div class="box">
		<div class="box-bd">
			<h1 class="si-title">
				Thank You for your recent purchase of EasyBlog!
			</h1>
			<p>
				Thank you for your recent purchase of EasyBlog, the best blogging extension for Joomla!
				<br /><br />
				Before you are able to access the EasyBlog, you will need to proceed with the Initial Setup.
			</p>

			<div id="svg__ani" class="lottie"></div>

			<div class="actions">
				<a href="<?php echo JURI::root();?>administrator/index.php?option=com_easyblog&amp;setup=true" class="btn btn-install">Continue with Installation &raquo;</a>
			</div>
			<div style="clear:both;"></div>
		</div>
	</div>
</div>


<script>
var animationRemote = bodymovin.loadAnimation({
	container: document.getElementById('svg__ani'),
	path: '<?php echo JURI::root();?>administrator/components/com_easyblog/setup/assets/images/setup.json',
	autoplay: true,
	renderer: 'svg',
	loop: false
});
</script>
