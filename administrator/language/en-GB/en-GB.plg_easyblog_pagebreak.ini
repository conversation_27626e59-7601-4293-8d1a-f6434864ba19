; @package		EasyBlog
; @copyright	Copyright (C) 2010 Stack Ideas Private Limited. All rights reserved.
; @license		GNU/GPL, see LICENSE.php
;
; EasyBlog is free software. This version may have been modified pursuant
; to the GNU General Public License, and as distributed it includes or
; is derivative of works licensed under the GNU General Public License or
; other free or open source software licenses.
; See COPYRIGHT.php for copyright notices and details.

PLG_PAGEBREAK_ALL_PAGES="All Pages"
PLG_PAGEBREAK_ARTICLE_INDEX="Article Index"
PLG_PAGEBREAK_PAGE_NO="Page %1s"
PLG_PAGEBREAK_GT="&gt;"
PLG_PAGEBREAK_LT="&lt;"
PLG_PAGEBREAK_HIDE="Hide"
PLG_PAGEBREAK_NEXT="Next"
PLG_PAGEBREAK_NO="No"
PLG_PAGEBREAK_PREV="Prev"
PLG_PAGEBREAK_SHOW="Show"
PLG_PAGEBREAK_SHOW_ALL="Show all"
PLG_PAGEBREAK_SITE_TITLE="Site Title"
PLG_PAGEBREAK_TABLE_OF_CONTENTS="Table of Contents"
PLG_PAGEBREAK_YES="Yes"
PLG_PAGEBREAK_PAGE="Page"
PLG_PAGEBREAK_USAGE="&lt;hr class=&quot;system-pagebreak&quot; /&gt; <b>OR</b><br />&lt;hr class=&quot;system-pagebreak&quot; title=&quot;The page title&quot; /&gt; <b>OR</b> <br />&lt;hr class=&quot;system-pagebreak&quot; alt=&quot;The first page&quot; /&gt; <b>OR</b> <br />&lt;hr class=&quot;system-pagebreak&quot; title=&quot;The page title&quot; alt=&quot;The first page&quot; /&gt; <b>OR</b> <br />&lt;hr class=&quot;system-pagebreak&quot; alt=&quot;The first page&quot; title=&quot;The page title&quot; /&gt;"

PARAMENABLED="Select whether the Plugin is enabled."
PARAMSITETITLE="title and heading attibutes from Plugin added to Site Title tag"
PARAMSITETOC="Display a table of contents on multi-page Articles."
PARAMSITESHOWALL="Allow Users to select the Show All feature"
