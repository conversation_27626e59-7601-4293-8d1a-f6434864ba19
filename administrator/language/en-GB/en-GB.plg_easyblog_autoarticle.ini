; @package		EasyBlog
; @copyright	Copyright (C) 2020 Stack Ideas Private Limited. All rights reserved.
; @license		GNU/GPL, see LICENSE.php
;
; EasyBlog is free software. This version may have been modified pursuant
; to the GNU General Public License, and as distributed it includes or
; is derivative of works licensed under the GNU General Public License or
; other free or open source software licenses.

PLG_EASYBLOG_AUTOARTICLE="EasyBlog - Auto Article"
PLG_EASYBLOG_AUTOARTICLE_XML_DESCRIPTION="This plugin is responsible to auto article creation."
PLG_EASYBLOG_AUTOARTICLE_ARTICLE_STATUS="Default article status"
PLG_EASYBLOG_AUTOARTICLE_ARTICLE_STATUS_DESC="Default article status"
PLG_EASYBLOG_AUTOARTICLE_ARTICLE_PERMISSION="Article's permission"
PLG_EASYBLOG_AUTOARTICLE_ARTICLE_PERMISSION_DESC="Article's permission"
PLG_EASYBLOG_AUTOARTICLE_ARTICLE_FRONTEND="Frontend"
PLG_EASYBLOG_AUTOARTICLE_ARTICLE_FRONTEND_DESC="Whether or not to show joomla article at frontpage."
PLG_EASYBLOG_AUTOARTICLE_ARTICLE_PUBLISH_AUTOMATICALLY="Automatically unpublish"
PLG_EASYBLOG_AUTOARTICLE_ARTICLE_PUBLISH_AUTOMATICALLY_DESC="Automatically unpublish content items once a blog post is deleted."
PLG_EASYBLOG_AUTOARTICLE_ARTICLE_DEFAULT_CATEGORY="Default article's category"
PLG_EASYBLOG_AUTOARTICLE_ARTICLE_DEFAULT_CATEGORY_DESC="Specify the default joomla category in your article."
PLG_EASYBLOG_AUTOARTICLE_ARTICLE_AUTO_MAP="Auto map Joomla category"
PLG_EASYBLOG_AUTOARTICLE_ARTICLE_AUTO_MAP_DESC="If enabled, EasyBlog will try to map the category from your Joomla. If mapping failed, EasyBlog fall back to the default category defined in above."
PLG_EASYBLOG_AUTOARTICLE_ARTICLE_SHOW_READMORE="Show Readmore"
PLG_EASYBLOG_AUTOARTICLE_ARTICLE_SHOW_READMORE_DESC="Set to show readmore into the article content."
PLG_EASYBLOG_AUTOARTICLE_ARTICLE_READMORE="Read More"
