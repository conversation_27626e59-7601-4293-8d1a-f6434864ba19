<?xml version="1.0" encoding="utf-8"?>
<extension type="module" version="3.0" client="site" method="upgrade">
	<name>EasyBlog - Ticker Module</name>
	<author>Stack Ideas Sdn Bhd</author>
	<creationDate>28th July 2025</creationDate>
	<copyright>Copyright 2009 - 2015 Stack Ideas Sdn Bhd. All rights reserved.</copyright>
	<license>GPL License v2</license>
	<authorEmail><EMAIL></authorEmail>
	<authorUrl>http://stackideas.com</authorUrl>
	<version>6.0.16</version>
	<description><![CDATA[Displays ticker-like view for latest post]]></description>
	<languages>
		<language tag="en-GB">en-GB.mod_easyblogticker.ini</language>
	</languages>
	<files>
		<folder>assets</folder>
		<folder>tmpl</folder>
		<filename module="mod_easyblogticker">mod_easyblogticker.php</filename>
		<filename>mod_easyblogticker.xml</filename>
	</files>
	<config>
		<fields name="params">
			<fieldset name="basic" addfieldpath="/administrator/components/com_easyblog/elements">
				<field name="count" type="text" default="5" class="input-mini text-center" label="COM_EB_POSTS_PER_PAGE" description="COM_EB_POSTS_PER_PAGE_DESC" />
				<field name="truncate_title" type="text" default="0" class="input-mini text-center" label="MOD_EASYBLOGTICKER_TRUNCATE_TITLE" description="MOD_EASYBLOGTICKER_TRUNCATE_TITLE_DESC" />
				<field name="catid" type="categories" default="0" label="MOD_EASYBLOG_SELECT_CATEGORY" description="MOD_EASYBLOG_SELECT_CATEGORY_DESC" />
			</fieldset>

			<fieldset name="advanced">
				<field name="layout" type="modulelayout" label="JFIELD_ALT_LAYOUT_LABEL" description="JFIELD_ALT_MODULE_LAYOUT_DESC" />
				<field name="moduleclass_sfx" type="text" label="COM_MODULES_FIELD_MODULECLASS_SFX_LABEL" description="COM_MODULES_FIELD_MODULECLASS_SFX_DESC" />
				<field  name="cache" type="list" default="0" label="COM_MODULES_FIELD_CACHING_LABEL" description="COM_MODULES_FIELD_CACHING_DESC" >
					<option value="1">JGLOBAL_USE_GLOBAL</option>
					<option value="0">COM_MODULES_FIELD_VALUE_NOCACHING
					</option>
				</field>
				<field name="cache_time" type="text" default="900" label="COM_MODULES_FIELD_CACHE_TIME_LABEL" description="COM_MODULES_FIELD_CACHE_TIME_DESC" />
			</fieldset>
		</fields>
	</config>
	<updateservers>
		<server type="extension" priority="1" name="StackIdeas Modules and Plugins">https://stackideas.com/joomla4compat.xml</server>
	</updateservers>
</extension>
