<?xml version="1.0" encoding="utf-8"?>
<extension type="module" version="3.0" client="site" method="upgrade">
	<name>EasyBlog - Subscribers Listing Module</name>
	<author>Stack Ideas Sdn Bhd</author>
	<creationDate>28th July 2025</creationDate>
	<copyright>Copyright 2009 - 2015 Stack Ideas Sdn Bhd. All rights reserved.</copyright>
	<license>GPL License v2</license>
	<authorEmail><EMAIL></authorEmail>
	<authorUrl>http://stackideas.com</authorUrl>
	<version>6.0.16</version>
	<description><![CDATA[This module renders a list of subscribers for the current blog post that is being viewed in EasyBlog.]]></description>
	<languages>
		<language tag="en-GB">en-GB.mod_easyblogsubscribers.ini</language>
	</languages>
	<files>
		<folder>tmpl</folder>
		<filename module="mod_easyblogsubscribers">mod_easyblogsubscribers.php</filename>
		<filename>mod_easyblogsubscribers.xml</filename>
	</files>
	<config>
		<fields name="params" addfieldpath="/administrator/components/com_easyblog/elements">
			<fieldset name="basic">
				<field name="subscription_type" type="list" default="site" label="MOD_EASYBLOG_SUBSCRIPTION_TYPE" description="MOD_EASYBLOG_SUBSCRIPTION_TYPE_DESC">
					<option value="blogger">MOD_EASYBLOG_SUBSCRIPTION_TYPE_AUTHOR</option>
					<option value="categories">MOD_EASYBLOG_SUBSCRIPTION_TYPE_CATEGORY</option>
					<option value="site">MOD_EASYBLOG_SUBSCRIPTION_TYPE_SITE</option>
					<option value="teamblog">MOD_EASYBLOG_SUBSCRIPTION_TYPE_TEAM</option>
					<option value="entry">MOD_EASYBLOG_SUBSCRIPTION_TYPE_ENTRY</option>
				</field>
				<field name="placeholder" type="placeholder" label=""/>
			</fieldset>
			<fieldset name="advanced">
				<field name="layout" type="modulelayout" label="JFIELD_ALT_LAYOUT_LABEL" description="JFIELD_ALT_MODULE_LAYOUT_DESC" />
				<field name="moduleclass_sfx" type="text" label="COM_MODULES_FIELD_MODULECLASS_SFX_LABEL" description="COM_MODULES_FIELD_MODULECLASS_SFX_DESC" />
				<field name="cache" type="list" default="0" label="COM_MODULES_FIELD_CACHING_LABEL" description="COM_MODULES_FIELD_CACHING_DESC" >
					<option value="1">JGLOBAL_USE_GLOBAL</option>
					<option value="0">COM_MODULES_FIELD_VALUE_NOCACHING</option>
				</field>
				<field name="cache_time" type="text" default="900" label="COM_MODULES_FIELD_CACHE_TIME_LABEL" description="COM_MODULES_FIELD_CACHE_TIME_DESC" />
			</fieldset>
		</fields>
	</config>
	<updateservers>
		<server type="extension" priority="1" name="StackIdeas Modules and Plugins">https://stackideas.com/joomla4compat.xml</server>
	</updateservers>
</extension>