<?xml version="1.0" encoding="utf-8"?>
<extension type="module" version="3.0" client="site" method="upgrade">
	<name>EasyBlog - Archive Module</name>
	<author>Stack Ideas Sdn Bhd</author>
	<creationDate>28th July 2025</creationDate>
	<copyright>Copyright 2009 - 2017 Stack Ideas Sdn Bhd. All rights reserved.</copyright>
	<license>GPL License v2</license>
	<authorEmail><EMAIL></authorEmail>
	<authorUrl>http://stackideas.com</authorUrl>
	<version>6.0.16</version>
	<description><![CDATA[The module displays a list of archived months in years for blog posts created within EasyBlog.]]></description>
	<languages>
		<language tag="en-GB">en-GB.mod_easyblogarchive.ini</language>
	</languages>
	<files>
		<folder>styles</folder>
		<folder>tmpl</folder>
		<filename module="mod_easyblogarchive">mod_easyblogarchive.php</filename>
		<filename>mod_easyblogarchive.xml</filename>
	</files>
	<config>
		<fields name="params" addfieldpath="/administrator/components/com_easyblog/elements">
			<fieldset name="basic">
				<field name="filter" type="list" default="all" label="MOD_EASYBLOG_FILTER_RULE" description="MOD_EASYBLOG_FILTER_RULE_DESC">
					<option value="all">MOD_EASYBLOG_FILTER_TYPE_ALL</option>
					<option value="blogger">MOD_EASYBLOG_FILTER_TYPE_BLOGGER</option>
					<option value="team">MOD_EASYBLOG_FILTER_TYPE_TEAM</option>
				</field>

				<field name="bloggerId" type="authors" default="" label="MOD_EASYBLOG_SELECT_AUTHOR" description="MOD_EASYBLOG_SELECT_AUTHOR_DESC" />
				<field name="teamId" type="team" default="" label="MOD_EASYBLOG_SELECT_TEAM" description="MOD_EASYBLOG_SELECT_TEAM_DESC"  />

				<field name="filterType" type="list" default="archives" label="MOD_EASYBLOG_FILTER_TYPE" description="MOD_EASYBLOG_FILTER_TYPE_DESC">
					<option value="normal">MOD_EASYBLOG_FILTER_TYPE_NORMAL</option>
					<option value="archives">MOD_EASYBLOG_FILTER_TYPE_ARCHIVES</option>
				</field>

				<field name="count" type="text" default="0" class="input-mini text-center" label="MOD_EASYBLOG_TOTAL_YEARS" description="MOD_EASYBLOG_TOTAL_YEARS_DESC" />
				<field name="showfuture" type="boolean" default="1" label="MOD_EASYBLOG_SHOW_FUTURE_MONTHS" description="MOD_EASYBLOG_SHOW_FUTURE_MONTHS_DESC" />
				<field name="showempty" type="boolean" default="1" label="MOD_EASYBLOG_SHOW_EMPTY_MONTHS" description="MOD_EASYBLOG_SHOW_EMPTY_MONTHS_DESC" />
				<field name="showemptyyear" type="boolean" default="1" label="MOD_EASYBLOG_SHOW_EMPTY_YEARS" description="MOD_EASYBLOG_SHOW_EMPTY_YEARS_DESC" />
				<field name="collapse" type="boolean" default="0" label="MOD_EASYBLOG_COLLAPSE_MONTHS" description="MOD_EASYBLOG_COLLAPSE_MONTHS_DESC" />

				<field name="order" type="list" default="asc" label="MOD_EASYBLOG_MONTHS_ORDERING" description="MOD_EASYBLOG_MONTHS_ORDERING_DESC">
					<option value="asc">MOD_EASYBLOG_SORT_ASC</option>
					<option value="desc">MOD_EASYBLOG_SORT_DESC</option>
				</field>

				<field name="catid" type="multicategories" label="MOD_EASYBLOG_SELECT_MULTIPLE_CATEGORIES" description="MOD_EASYBLOG_SELECT_MULTIPLE_CATEGORIES_DESC" />
				<field name="excatid" type="multicategories" label="MOD_EASYBLOG_EXCLUDE_MULTIPLE_CATEGORIES" description="MOD_EASYBLOG_EXCLUDE_MULTIPLE_CATEGORIES_DESC" />
			</fieldset>
			<fieldset name="advanced">
				<field name="layout" type="modulelayout" label="JFIELD_ALT_LAYOUT_LABEL" description="JFIELD_ALT_MODULE_LAYOUT_DESC" />
				<field name="moduleclass_sfx" type="text" label="COM_MODULES_FIELD_MODULECLASS_SFX_LABEL" description="COM_MODULES_FIELD_MODULECLASS_SFX_DESC" />
				<field name="cache" type="list" default="0" label="COM_MODULES_FIELD_CACHING_LABEL" description="COM_MODULES_FIELD_CACHING_DESC">
					<option value="1">JGLOBAL_USE_GLOBAL</option>
					<option value="0">COM_MODULES_FIELD_VALUE_NOCACHING</option>
				</field>
				<field name="cache_time" type="text" default="900" label="COM_MODULES_FIELD_CACHE_TIME_LABEL" description="COM_MODULES_FIELD_CACHE_TIME_DESC" />
			</fieldset>
		</fields>
	</config>
	<updateservers>
		<server type="extension" priority="1" name="StackIdeas Modules and Plugins">https://stackideas.com/joomla4compat.xml</server>
	</updateservers>
</extension>