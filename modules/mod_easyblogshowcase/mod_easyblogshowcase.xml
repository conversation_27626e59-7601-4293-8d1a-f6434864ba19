<?xml version="1.0" encoding="utf-8"?>
<extension type="module" version="3.0" client="site" method="upgrade">
	<name>EasyBlog - Showcase Module</name>
	<author>Stack Ideas Sdn Bhd</author>
	<creationDate>28th July 2025</creationDate>
	<copyright>Copyright 2009 - 2015 Stack Ideas Sdn Bhd. All rights reserved.</copyright>
	<license>GPL License v2</license>
	<authorEmail><EMAIL></authorEmail>
	<authorUrl>http://stackideas.com</authorUrl>
	<version>6.0.16</version>
	<description><![CDATA[This module allows you to choose several display mode for the posts on the site.]]></description>
	<languages>
		<language tag="en-GB">en-GB.mod_easyblogshowcase.ini</language>
	</languages>
	<files>
		<folder>tmpl</folder>
		<filename>helper.php</filename>
		<filename module="mod_easyblogshowcase">mod_easyblogshowcase.php</filename>
		<filename>mod_easyblogshowcase.xml</filename>
	</files>
	<config>

		<fields name="params">
			<fieldset name="basic" addfieldpath="/administrator/components/com_easyblog/elements">
				<field name="layout" type="modulelayout" label="MOD_EASYBLOG_LAYOUT" description="MOD_EASYBLOG_LAYOUT_DESC" />
				<field name="navigation_type" showon="layout:_:default" type="list" default="default" label="MOD_EB_NAVIGATION_TYPE" description="MOD_EB_NAVIGATION_TYPE_DESC">
					<option value="default">MOD_EASYBLOG_NAVIGATION_TYPE_ARROW</option>
					<option value="numbering">MOD_EASYBLOG_NAVIGATION_TYPE_NUMBERING</option>
					<option value="none">MOD_EASYBLOG_NAVIGATION_TYPE_NONE</option>
				</field>

				<field name="count" type="text" class="input-mini text-center" default="5" label="MOD_EASYBLOG_TOTAL_POSTS" description="MOD_EASYBLOG_TOTAL_POSTS_DESC" />
				<field name="catid" type="multicategories" default="0" label="MOD_EASYBLOG_SELECT_MULTIPLE_CATEGORIES" description="MOD_EASYBLOG_SELECT_MULTIPLE_CATEGORIES_DESC" />
				<field name="subcat" type="boolean" default="1" label="MOD_EASYBLOG_INCLUDE_SUBCATEGORIES" description="MOD_EASYBLOG_INCLUDE_SUBCATEGORIES_DESC" />
				<field name="textlimit" type="text" default="200" class="input-mini text-center" label="MOD_EASYBLOG_TRUNCATE_POST_CONTENT" description="MOD_EASYBLOG_TRUNCATE_POST_CONTENT_DESC" />
				<field name="showposttype" type="list" default="featured" label="MOD_EASYBLOG_SHOWCASE_SOURCE" description="MOD_EASYBLOG_SHOWCASE_SOURCE">
					<option value="all">MOD_EASYBLOG_SHOWCASE_SOURCE_LATEST_AND_FEATURED</option>
					<option value="latestOnly">MOD_EASYBLOG_SHOWCASE_SOURCE_LATEST_ONLY</option>
					<option value="featured">MOD_EASYBLOG_SHOWCASE_SOURCE_FEATURED_ONLY</option>
				</field>
				<field name="contentfrom" type="list" default="content" label="MOD_EASYBLOG_CONTENT_SOURCE" description="MOD_EASYBLOG_CONTENT_SOURCE_DESC">
					<option value="intro">MOD_EASYBLOG_SHOW_INTROTEXT_ONLY</option>
					<option value="content">MOD_EASYBLOG_SHOW_MAIN_CONTENT_ONLY</option>
				</field>
				<field name="photo_show" type="boolean" default="1" label="MOD_EASYBLOG_DISPLAY_POST_COVER" description="MOD_EASYBLOG_DISPLAY_POST_COVER_DESC" />
				<field name="show_cover_placeholder" type="boolean" default="1" label="MOD_EASYBLOG_DISPLAY_POST_COVER_PLACEHOLDER" description="MOD_EASYBLOG_DISPLAY_POST_COVER_PLACEHOLDER_DESC" />
				<field name="photo_legacy" type="boolean" default="1" label="MOD_EASYBLOG_PICK_FIRST_IMAGE_AS_COVER" description="MOD_EASYBLOG_PICK_FIRST_IMAGE_AS_COVER_DESC" />
				<field name="photo_layout" type="cover" label="MOD_EASYBLOG_COVER_LAYOUT_STYLE" description="MOD_EASYBLOG_COVER_LAYOUT_STYLE_DESC" crop="1" full="0" disablefull="1" defaultwidth="300" defaultheight="200" />
				<field name="showreadmore" type="boolean" default="1" label="MOD_EASYBLOG_DISPLAY_READMORE"  description="MOD_EASYBLOG_DISPLAY_READMORE_DESC" />
				<field name="contentauthor" type="boolean" default="1" label="MOD_EASYBLOG_DISPLAY_AUTHOR" description="MOD_EASYBLOG_DISPLAY_AUTHOR_DESC" />
				<field name="authoravatar" type="boolean" default="1" label="MOD_EASYBLOG_DISPLAY_AUTHOR_AVATAR"  description="MOD_EASYBLOG_DISPLAY_AUTHOR_AVATAR_DESC"  />
				<field name="contentdate" type="boolean" default="1" label="MOD_EASYBLOG_DISPLAY_DATE" description="MOD_EASYBLOG_DISPLAY_DATE_DESC" />
				<field name="showratings" type="boolean" default="1" label="MOD_EASYBLOG_DISPLAY_RATINGS" description="MOD_EASYBLOG_DISPLAY_RATINGS_DESC" />
				<field name="autoshuffle" type="boolean" default="0" label="MOD_EASYBLOG_RANDOMIZE_POSTS" description="MOD_EASYBLOG_RANDOMIZE_POSTS_DESC" />
				<field name="display_showall" type="boolean" default="0" label="COM_EB_MODULE_DISPLAY_VIEW_ALL" description="COM_EB_MODULE_DISPLAY_VIEW_ALL_DESC" />
				<field name="autorotate" type="boolean" default="1" label="MOD_EASYBLOG_AUTOROTATE_SLIDES" description="MOD_EASYBLOG_AUTOROTATE_SLIDES_DESC" />
				<field name="autorotate_seconds" type="text" default="30" class="input-mini text-center" label="MOD_EASYBLOG_AUTOROTATE_SLIDES_TIMER" description="MOD_EASYBLOG_AUTOROTATE_SLIDES_TIMER_DESC" />
			</fieldset>

			<fieldset name="advanced">
				<field name="moduleclass_sfx" type="text" label="COM_MODULES_FIELD_MODULECLASS_SFX_LABEL" description="COM_MODULES_FIELD_MODULECLASS_SFX_DESC" />
				<field  name="cache" type="list" default="0" label="COM_MODULES_FIELD_CACHING_LABEL" description="COM_MODULES_FIELD_CACHING_DESC" >
					<option value="1">JGLOBAL_USE_GLOBAL</option>
					<option value="0">COM_MODULES_FIELD_VALUE_NOCACHING
					</option>
				</field>
				<field name="cache_time" type="text" default="900" label="COM_MODULES_FIELD_CACHE_TIME_LABEL" description="COM_MODULES_FIELD_CACHE_TIME_DESC" />
			</fieldset>
		</fields>
	</config>
	<updateservers>
		<server type="extension" priority="1" name="StackIdeas Modules and Plugins">https://stackideas.com/joomla4compat.xml</server>
	</updateservers>
</extension>
