// JavaScript snippet to add info popup to Dates/Prices section
// Target: Sunday 26th July 2026 on /holidays/kilimanjaro-the-long-way

(function() {
    'use strict';
    
    // Check if we're on the correct page
    if (window.location.pathname !== '/holidays/kilimanjaro-the-long-way') {
        return;
    }
    
    // SVG icon content - award/badge icon
    const awardIconSVG = '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M10.92 12.443L12 11.623L13.07 12.433C13.46 12.723 13.99 12.353 13.85 11.883L13.43 10.523L14.63 9.57299C15 9.29299 14.79 8.69299 14.31 8.69299H12.91L12.48 7.35299C12.33 6.89299 11.68 6.89299 11.53 7.35299L11.09 8.69299H9.68C9.21 8.69299 9 9.29299 9.37 9.58299L10.56 10.533L10.14 11.893C10 12.363 10.53 12.733 10.92 12.443ZM6 21.303C6 21.983 6.67 22.463 7.32 22.253L12 20.693L16.68 22.253C17.33 22.473 18 21.993 18 21.303V14.973C19.24 13.563 20 11.723 20 9.69299C20 5.27299 16.42 1.69299 12 1.69299C7.58 1.69299 4 5.27299 4 9.69299C4 11.723 4.76 13.563 6 14.973V21.303ZM12 3.69299C15.31 3.69299 18 6.38299 18 9.69299C18 13.003 15.31 15.693 12 15.693C8.69 15.693 6 13.003 6 9.69299C6 6.38299 8.69 3.69299 12 3.69299Z" fill="#FE7720"/></svg>';

    // Info icon for popovers (smaller version)
    const infoIconSVG = '<svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M9.1665 6.33342H10.8332V8.00008H9.1665V6.33342ZM9.1665 9.66675H10.8332V14.6667H9.1665V9.66675ZM9.99984 2.16675C5.39984 2.16675 1.6665 5.90008 1.6665 10.5001C1.6665 15.1001 5.39984 18.8334 9.99984 18.8334C14.5998 18.8334 18.3332 15.1001 18.3332 10.5001C18.3332 5.90008 14.5998 2.16675 9.99984 2.16675ZM9.99984 17.1667C6.32484 17.1667 3.33317 14.1751 3.33317 10.5001C3.33317 6.82508 6.32484 3.83341 9.99984 3.83341C13.6748 3.83341 16.6665 6.82508 16.6665 10.5001C16.6665 14.1751 13.6748 17.1667 9.99984 17.1667Z" fill="currentColor"/></svg>';
    
    // Function to add info popup functionality
    function addInfoPopup() {
        // Find the 2026 accordion section
        const accordion2026 = document.querySelector('#price-year-title-2026');
        if (!accordion2026) {
            console.log('2026 accordion section not found');
            return;
        }
        
        // Find the accordion button and add info icon
        const accordionButton = accordion2026.querySelector('button');
        if (accordionButton) {
            const buttonSpan = accordionButton.querySelector('span');
            if (buttonSpan) {
                // Create info icon element
                const infoIcon = document.createElement('span');
                infoIcon.className = 'info-icon';
                infoIcon.style.cssText = `
                    display: inline-block;
                    width: 24px;
                    height: 24px;
                    margin-left: 10px;
                    cursor: pointer;
                    color: #FE7720;
                    vertical-align: middle;
                    transform: translateY(-2px);
                `;
                infoIcon.innerHTML = infoIconSVG;
                
                // Add popover attributes
                infoIcon.setAttribute('data-bs-toggle', 'popover');
                infoIcon.setAttribute('data-bs-trigger', 'hover focus');
                infoIcon.setAttribute('data-bs-placement', 'top');
                infoIcon.setAttribute('data-bs-html', 'true');
                infoIcon.setAttribute('data-bs-title', 'Limited Availability');
                infoIcon.setAttribute('data-bs-content', '<div class="popover-content"><p>This departure has limited spaces available. Book early to secure your place on this popular trek.</p></div>');
                infoIcon.setAttribute('data-info-id', 'kilimanjaro-2026-limited');
                
                // Add icon after the year text
                infoIcon.innerHTML = awardIconSVG; // Use award icon for accordion title
                buttonSpan.appendChild(infoIcon);
            }
        }
        
        // Find the 2026 accordion content and look for Sunday 19th July 2026
        const accordionContent = document.querySelector('#price-year-item-2026');
        if (accordionContent) {
            // Find all date rows in the 2026 section
            const dateRows = accordionContent.querySelectorAll('.row.py-4');

            dateRows.forEach(function(row) {
                // Check if this row contains Sunday 19th July 2026
                const dateText = row.querySelector('p.zen-text--text-lg');
                if (dateText && dateText.textContent.includes('Sunday 19') && dateText.textContent.includes('July 2026')) {
                    // Add orange background with padding to this specific row
                    row.style.cssText = `
                        background-color: #fff8f0 !important;
                        padding-left: 15px !important;
                        padding-right: 15px !important;
                        padding-top: 10px !important;
                        margin-left: -15px;
                        margin-right: -15px;
                        margin-top: -10px !important;
                    `;

                    // Find the div that wraps the p.zen-text element containing the dates
                    const dateTextElement = row.querySelector('p.zen-text--text-lg');
                    const dateWrapperDiv = dateTextElement ? dateTextElement.parentElement : null;
                    if (dateWrapperDiv) {
                        // Create exclusive text content (no div wrapper)
                        const exclusiveSpan = document.createElement('span');
                        exclusiveSpan.style.cssText = `
                            padding: 0;
                            margin: 0;
                            font-size: 12px;
                            display: block;
                            width: 100%;
                        `;

                        // Create award icon (smaller version for before EXCLUSIVE)
                        const awardIcon = document.createElement('span');
                        awardIcon.innerHTML = '<svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M10.92 12.443L12 11.623L13.07 12.433C13.46 12.723 13.99 12.353 13.85 11.883L13.43 10.523L14.63 9.57299C15 9.29299 14.79 8.69299 14.31 8.69299H12.91L12.48 7.35299C12.33 6.89299 11.68 6.89299 11.53 7.35299L11.09 8.69299H9.68C9.21 8.69299 9 9.29299 9.37 9.58299L10.56 10.533L10.14 11.893C10 12.363 10.53 12.733 10.92 12.443ZM6 21.303C6 21.983 6.67 22.463 7.32 22.253L12 20.693L16.68 22.253C17.33 22.473 18 21.993 18 21.303V14.973C19.24 13.563 20 11.723 20 9.69299C20 5.27299 16.42 1.69299 12 1.69299C7.58 1.69299 4 5.27299 4 9.69299C4 11.723 4.76 13.563 6 14.973V21.303ZM12 3.69299C15.31 3.69299 18 6.38299 18 9.69299C18 13.003 15.31 15.693 12 15.693C8.69 15.693 6 13.003 6 9.69299C6 6.38299 8.69 3.69299 12 3.69299Z" fill="#FE7720"/></svg>';
                        awardIcon.style.cssText = `
                            margin-right: 6px;
                            vertical-align: middle;
                            display: inline-block;
                        `;

                        // Create orange "EXCLUSIVE" part
                        const exclusivePart = document.createElement('span');
                        exclusivePart.style.cssText = `
                            color: #FE7720;
                            font-weight: 700;
                        `;
                        exclusivePart.textContent = 'EXCLUSIVE';

                        // Create grey rest of text
                        const restText = document.createElement('span');
                        restText.style.cssText = `
                            color: #4f4f4f;
                            font-weight: 400;
                        `;
                        restText.textContent = ' - Trek with our Brand Ambassador Jon Foxy Davies ';

                        // Create info icon for the exclusive text
                        const exclusiveIcon = document.createElement('span');
                        exclusiveIcon.className = 'info-icon';
                        exclusiveIcon.style.cssText = `
                            display: inline-block;
                            width: 20px;
                            height: 20px;
                            margin-left: 4px;
                            cursor: pointer;
                            color: #4f4f4f;
                            vertical-align: middle;
                            transform: translateY(-2px);
                        `;
                        exclusiveIcon.innerHTML = infoIconSVG;

                        // Add popover attributes to exclusive icon
                        exclusiveIcon.setAttribute('data-bs-toggle', 'popover');
                        exclusiveIcon.setAttribute('data-bs-trigger', 'hover focus');
                        exclusiveIcon.setAttribute('data-bs-placement', 'top');
                        exclusiveIcon.setAttribute('data-bs-html', 'true');
                        exclusiveIcon.setAttribute('data-bs-title', 'Exclusive Experience');
                        exclusiveIcon.setAttribute('data-bs-content', '<div class="popover-content"><p><strong>Climb Kilimanjaro with Welsh Rugby Legend & Lions Star Jon \'Foxy\' Davies</strong><br>Join our Brand Ambassador on the adventure of a lifetime - plus get FREE annual access to our Ultimate Trek Prep Online Programme worth £150!</p></div>');
                        exclusiveIcon.setAttribute('data-info-id', 'kilimanjaro-foxy-exclusive');

                        // Assemble exclusive text parts
                        exclusiveSpan.appendChild(awardIcon);
                        exclusiveSpan.appendChild(exclusivePart);
                        exclusiveSpan.appendChild(restText);
                        exclusiveSpan.appendChild(exclusiveIcon);

                        // Insert exclusive text at the beginning of the date wrapper div (above the dates)
                        dateWrapperDiv.insertBefore(exclusiveSpan, dateWrapperDiv.firstChild);

                        // Find and style the flex container for desktop alignment
                        const flexContainer = row.querySelector('.col-12.d-flex');
                        if (flexContainer) {
                            flexContainer.style.setProperty('align-items', 'flex-end', 'important');
                        }
                    }
                }
            });
        }
        
        // Initialize Bootstrap popovers for the new elements
        initializePopovers();
    }
    
    // Function to initialize Bootstrap popovers
    function initializePopovers() {
        // Find all new info icons and initialize popovers
        const newInfoIcons = document.querySelectorAll('.info-icon[data-bs-toggle="popover"]');
        
        newInfoIcons.forEach(function(icon) {
            // Skip if already initialized
            if (icon.hasAttribute('data-popover-initialized')) {
                return;
            }
            
            // Mark as initialized
            icon.setAttribute('data-popover-initialized', 'true');
            
            // Create popover instance
            const popover = new bootstrap.Popover(icon, {
                html: true,
                trigger: 'manual',
                placement: 'right',
                fallbackPlacements: ['top', 'left', 'bottom'],
                container: 'body',
                sanitize: false,
                allowList: {
                    'strong': [],
                    'b': [],
                    'em': [],
                    'i': [],
                    'br': [],
                    'p': [],
                    'a': ['href', 'title', 'target'],
                    'div': ['class'],
                    'span': ['class']
                },
                delay: { show: 300, hide: 500 }
            });
            
            // Add hover event listeners
            let showTimeout, hideTimeout;
            
            icon.addEventListener('mouseenter', function() {
                clearTimeout(hideTimeout);
                showTimeout = setTimeout(function() {
                    popover.show();
                }, 300);
            });
            
            icon.addEventListener('mouseleave', function() {
                clearTimeout(showTimeout);
                hideTimeout = setTimeout(function() {
                    popover.hide();
                }, 500);
            });
            
            // Keep popover open when hovering over it
            icon.addEventListener('shown.bs.popover', function() {
                const popoverElement = document.querySelector('.popover');
                if (popoverElement) {
                    popoverElement.addEventListener('mouseenter', function() {
                        clearTimeout(hideTimeout);
                    });
                    
                    popoverElement.addEventListener('mouseleave', function() {
                        hideTimeout = setTimeout(function() {
                            popover.hide();
                        }, 500);
                    });
                }
            });
        });
    }
    
    // Flag to ensure we only run once
    let hasRun = false;

    // Function to attempt adding the popup
    function attemptAddPopup() {
        if (hasRun) return;

        const accordion2026 = document.querySelector('#price-year-title-2026');
        if (accordion2026) {
            hasRun = true;
            addInfoPopup();
            console.log('Info popup added to 2026 dates section');
        }
    }

    // MutationObserver to watch for dynamic content changes
    function setupMutationObserver() {
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    // Check if any added nodes contain our target element
                    for (let node of mutation.addedNodes) {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            if (node.querySelector && node.querySelector('#price-year-title-2026')) {
                                attemptAddPopup();
                                return;
                            }
                            if (node.id === 'price-year-title-2026') {
                                attemptAddPopup();
                                return;
                            }
                        }
                    }
                }
            });
        });

        // Start observing the dates/prices container
        const datesContainer = document.querySelector('#dates-pricing-accordion') || document.body;
        observer.observe(datesContainer, {
            childList: true,
            subtree: true
        });

        // Stop observing after 30 seconds to prevent memory leaks
        setTimeout(function() {
            observer.disconnect();
        }, 30000);
    }

    // Initialize on DOM ready
    function initialize() {
        // Try immediately in case content is already loaded
        attemptAddPopup();

        // Set up mutation observer for dynamic content
        if (!hasRun) {
            setupMutationObserver();
        }
    }

    // Start the process
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        initialize();
    }
    
})();
