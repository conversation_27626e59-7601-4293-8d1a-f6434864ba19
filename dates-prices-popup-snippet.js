// JavaScript snippet to add info popup to Dates/Prices section
// Target: Sunday 26th July 2026 on /holidays/kilimanjaro-the-long-way

(function() {
    'use strict';
    
    // Check if we're on the correct page
    if (window.location.pathname !== '/holidays/kilimanjaro-the-long-way') {
        return;
    }
    
    // SVG icon content (from templates/zenbase/icons/information.svg)
    const infoIconSVG = '<svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M9.1665 6.33342H10.8332V8.00008H9.1665V6.33342ZM9.1665 9.66675H10.8332V14.6667H9.1665V9.66675ZM9.99984 2.16675C5.39984 2.16675 1.6665 5.90008 1.6665 10.5001C1.6665 15.1001 5.39984 18.8334 9.99984 18.8334C14.5998 18.8334 18.3332 15.1001 18.3332 10.5001C18.3332 5.90008 14.5998 2.16675 9.99984 2.16675ZM9.99984 17.1667C6.32484 17.1667 3.33317 14.1751 3.33317 10.5001C3.33317 6.82508 6.32484 3.83341 9.99984 3.83341C13.6748 3.83341 16.6665 6.82508 16.6665 10.5001C16.6665 14.1751 13.6748 17.1667 9.99984 17.1667Z" fill="currentColor"/></svg>';
    
    // Function to add info popup functionality
    function addInfoPopup() {
        // Find the 2026 accordion section
        const accordion2026 = document.querySelector('#price-year-title-2026');
        if (!accordion2026) {
            console.log('2026 accordion section not found');
            return;
        }
        
        // Find the accordion button and add info icon
        const accordionButton = accordion2026.querySelector('button');
        if (accordionButton) {
            const buttonSpan = accordionButton.querySelector('span');
            if (buttonSpan) {
                // Create info icon element
                const infoIcon = document.createElement('span');
                infoIcon.className = 'info-icon';
                infoIcon.style.cssText = `
                    display: inline-block;
                    width: 20px;
                    height: 20px;
                    margin-left: 8px;
                    cursor: pointer;
                    color: #FE7720;
                    vertical-align: middle;
                    transform: translateY(-2px);
                `;
                infoIcon.innerHTML = infoIconSVG;
                
                // Add popover attributes
                infoIcon.setAttribute('data-bs-toggle', 'popover');
                infoIcon.setAttribute('data-bs-trigger', 'hover focus');
                infoIcon.setAttribute('data-bs-placement', 'top');
                infoIcon.setAttribute('data-bs-html', 'true');
                infoIcon.setAttribute('data-bs-title', 'Limited Availability');
                infoIcon.setAttribute('data-bs-content', '<div class="popover-content"><p>This departure has limited spaces available. Book early to secure your place on this popular trek.</p></div>');
                infoIcon.setAttribute('data-info-id', 'kilimanjaro-2026-limited');
                
                // Add icon after the year text
                buttonSpan.appendChild(infoIcon);
            }
        }
        
        // Find the 2026 accordion content and modify it
        const accordionContent = document.querySelector('#price-year-item-2026');
        if (accordionContent) {
            // Change background color
            accordionContent.style.backgroundColor = '#fff8f0'; // Light orange background
            
            // Find the container inside the accordion content
            const container = accordionContent.querySelector('.container');
            if (container) {
                // Create warning message element
                const warningMessage = document.createElement('div');
                warningMessage.style.cssText = `
                    background-color: #fff3cd;
                    border: 1px solid #ffeaa7;
                    border-radius: 8px;
                    padding: 12px 16px;
                    margin-bottom: 20px;
                    display: flex;
                    align-items: center;
                    gap: 10px;
                `;
                
                // Create warning icon
                const warningIcon = document.createElement('span');
                warningIcon.className = 'info-icon';
                warningIcon.style.cssText = `
                    display: inline-block;
                    width: 20px;
                    height: 20px;
                    color: #856404;
                    flex-shrink: 0;
                `;
                warningIcon.innerHTML = infoIconSVG;
                
                // Add popover to warning icon
                warningIcon.setAttribute('data-bs-toggle', 'popover');
                warningIcon.setAttribute('data-bs-trigger', 'hover focus');
                warningIcon.setAttribute('data-bs-placement', 'right');
                warningIcon.setAttribute('data-bs-html', 'true');
                warningIcon.setAttribute('data-bs-title', 'Why Limited?');
                warningIcon.setAttribute('data-bs-content', '<div class="popover-content"><p>Due to high demand and permit restrictions, we can only offer a limited number of spaces for our 2026 Kilimanjaro departures.</p></div>');
                warningIcon.setAttribute('data-info-id', 'kilimanjaro-2026-warning');
                
                // Create warning text
                const warningText = document.createElement('span');
                warningText.style.cssText = `
                    color: #856404;
                    font-weight: 600;
                    font-size: 14px;
                `;
                warningText.textContent = 'Limited availability for 2026 departures - book early to avoid disappointment';
                
                // Assemble warning message
                warningMessage.appendChild(warningIcon);
                warningMessage.appendChild(warningText);
                
                // Insert warning message at the beginning of the container
                container.insertBefore(warningMessage, container.firstChild);
            }
        }
        
        // Initialize Bootstrap popovers for the new elements
        initializePopovers();
    }
    
    // Function to initialize Bootstrap popovers
    function initializePopovers() {
        // Find all new info icons and initialize popovers
        const newInfoIcons = document.querySelectorAll('.info-icon[data-bs-toggle="popover"]');
        
        newInfoIcons.forEach(function(icon) {
            // Skip if already initialized
            if (icon.hasAttribute('data-popover-initialized')) {
                return;
            }
            
            // Mark as initialized
            icon.setAttribute('data-popover-initialized', 'true');
            
            // Create popover instance
            const popover = new bootstrap.Popover(icon, {
                html: true,
                trigger: 'manual',
                placement: 'right',
                fallbackPlacements: ['top', 'left', 'bottom'],
                container: 'body',
                sanitize: false,
                allowList: {
                    'strong': [],
                    'b': [],
                    'em': [],
                    'i': [],
                    'br': [],
                    'p': [],
                    'a': ['href', 'title', 'target'],
                    'div': ['class'],
                    'span': ['class']
                },
                delay: { show: 300, hide: 500 }
            });
            
            // Add hover event listeners
            let showTimeout, hideTimeout;
            
            icon.addEventListener('mouseenter', function() {
                clearTimeout(hideTimeout);
                showTimeout = setTimeout(function() {
                    popover.show();
                }, 300);
            });
            
            icon.addEventListener('mouseleave', function() {
                clearTimeout(showTimeout);
                hideTimeout = setTimeout(function() {
                    popover.hide();
                }, 500);
            });
            
            // Keep popover open when hovering over it
            icon.addEventListener('shown.bs.popover', function() {
                const popoverElement = document.querySelector('.popover');
                if (popoverElement) {
                    popoverElement.addEventListener('mouseenter', function() {
                        clearTimeout(hideTimeout);
                    });
                    
                    popoverElement.addEventListener('mouseleave', function() {
                        hideTimeout = setTimeout(function() {
                            popover.hide();
                        }, 500);
                    });
                }
            });
        });
    }
    
    // Wait for DOM to be ready and dates/prices content to load
    function waitForContent() {
        const checkInterval = setInterval(function() {
            const accordion2026 = document.querySelector('#price-year-title-2026');
            if (accordion2026) {
                clearInterval(checkInterval);
                addInfoPopup();
            }
        }, 100);
        
        // Stop checking after 10 seconds
        setTimeout(function() {
            clearInterval(checkInterval);
        }, 10000);
    }
    
    // Start the process
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', waitForContent);
    } else {
        waitForContent();
    }
    
})();
