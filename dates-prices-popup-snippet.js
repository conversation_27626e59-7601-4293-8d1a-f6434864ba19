// JavaScript snippet to add info popup to Dates/Prices section
// Target: Sunday 26th July 2026 on /holidays/kilimanjaro-the-long-way

(function() {
    'use strict';
    
    // Check if we're on the correct page
    if (window.location.pathname !== '/holidays/kilimanjaro-the-long-way') {
        return;
    }
    
    // SVG icon content (from templates/zenbase/icons/information.svg)
    const infoIconSVG = '<svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M9.1665 6.33342H10.8332V8.00008H9.1665V6.33342ZM9.1665 9.66675H10.8332V14.6667H9.1665V9.66675ZM9.99984 2.16675C5.39984 2.16675 1.6665 5.90008 1.6665 10.5001C1.6665 15.1001 5.39984 18.8334 9.99984 18.8334C14.5998 18.8334 18.3332 15.1001 18.3332 10.5001C18.3332 5.90008 14.5998 2.16675 9.99984 2.16675ZM9.99984 17.1667C6.32484 17.1667 3.33317 14.1751 3.33317 10.5001C3.33317 6.82508 6.32484 3.83341 9.99984 3.83341C13.6748 3.83341 16.6665 6.82508 16.6665 10.5001C16.6665 14.1751 13.6748 17.1667 9.99984 17.1667Z" fill="currentColor"/></svg>';
    
    // Function to add info popup functionality
    function addInfoPopup() {
        // Find the 2026 accordion section
        const accordion2026 = document.querySelector('#price-year-title-2026');
        if (!accordion2026) {
            console.log('2026 accordion section not found');
            return;
        }
        
        // Find the accordion button and add info icon
        const accordionButton = accordion2026.querySelector('button');
        if (accordionButton) {
            const buttonSpan = accordionButton.querySelector('span');
            if (buttonSpan) {
                // Create info icon element
                const infoIcon = document.createElement('span');
                infoIcon.className = 'info-icon';
                infoIcon.style.cssText = `
                    display: inline-block;
                    width: 20px;
                    height: 20px;
                    margin-left: 8px;
                    cursor: pointer;
                    color: #FE7720;
                    vertical-align: middle;
                    transform: translateY(-2px);
                `;
                infoIcon.innerHTML = infoIconSVG;
                
                // Add popover attributes
                infoIcon.setAttribute('data-bs-toggle', 'popover');
                infoIcon.setAttribute('data-bs-trigger', 'hover focus');
                infoIcon.setAttribute('data-bs-placement', 'top');
                infoIcon.setAttribute('data-bs-html', 'true');
                infoIcon.setAttribute('data-bs-title', 'Limited Availability');
                infoIcon.setAttribute('data-bs-content', '<div class="popover-content"><p>This departure has limited spaces available. Book early to secure your place on this popular trek.</p></div>');
                infoIcon.setAttribute('data-info-id', 'kilimanjaro-2026-limited');
                
                // Add icon after the year text
                buttonSpan.appendChild(infoIcon);
            }
        }
        
        // Find the 2026 accordion content and look for Sunday 19th July 2026
        const accordionContent = document.querySelector('#price-year-item-2026');
        if (accordionContent) {
            // Find all date rows in the 2026 section
            const dateRows = accordionContent.querySelectorAll('.row.py-4');

            dateRows.forEach(function(row) {
                // Check if this row contains Sunday 19th July 2026
                const dateText = row.querySelector('p.zen-text--text-lg');
                if (dateText && dateText.textContent.includes('Sunday 19') && dateText.textContent.includes('July 2026')) {
                    // Add orange background with padding to this specific row
                    row.style.cssText = `
                        background-color: #fff8f0 !important;
                        padding-left: 15px !important;
                        padding-right: 15px !important;
                        border-radius: 8px;
                        margin-left: -15px;
                        margin-right: -15px;
                    `;

                    // Create exclusive text element
                    const exclusiveText = document.createElement('div');
                    exclusiveText.style.cssText = `
                        background-color: transparent;
                        padding: 0 0 15px 0;
                        margin-bottom: 15px;
                        border-bottom: 1px solid #ddd;
                        font-size: 14px;
                        color: #FE7720;
                        font-weight: 600;
                    `;

                    // Create the text content
                    const textSpan = document.createElement('span');
                    textSpan.textContent = 'EXCLUSIVE - Trek with our Brand Ambassador Jon Foxy Davies ';

                    // Create info icon for the exclusive text
                    const exclusiveIcon = document.createElement('span');
                    exclusiveIcon.className = 'info-icon';
                    exclusiveIcon.style.cssText = `
                        display: inline-block;
                        width: 20px;
                        height: 20px;
                        margin-left: 4px;
                        cursor: pointer;
                        color: #FE7720;
                        vertical-align: middle;
                        transform: translateY(-2px);
                    `;
                    exclusiveIcon.innerHTML = infoIconSVG;

                    // Add popover attributes to exclusive icon
                    exclusiveIcon.setAttribute('data-bs-toggle', 'popover');
                    exclusiveIcon.setAttribute('data-bs-trigger', 'hover focus');
                    exclusiveIcon.setAttribute('data-bs-placement', 'top');
                    exclusiveIcon.setAttribute('data-bs-html', 'true');
                    exclusiveIcon.setAttribute('data-bs-title', 'Exclusive Experience');
                    exclusiveIcon.setAttribute('data-bs-content', '<div class="popover-content"><p><strong>Climb Kilimanjaro with Welsh Rugby Legend & Lions Star Jon \'Foxy\' Davies</strong><br>Join our Brand Ambassador on the adventure of a lifetime - plus get FREE annual access to our Ultimate Trek Prep Online Programme worth £150!</p></div>');
                    exclusiveIcon.setAttribute('data-info-id', 'kilimanjaro-foxy-exclusive');

                    // Assemble exclusive text
                    exclusiveText.appendChild(textSpan);
                    exclusiveText.appendChild(exclusiveIcon);

                    // Insert exclusive text at the beginning of the row
                    row.insertBefore(exclusiveText, row.firstChild);
                }
            });
        }
        
        // Initialize Bootstrap popovers for the new elements
        initializePopovers();
    }
    
    // Function to initialize Bootstrap popovers
    function initializePopovers() {
        // Find all new info icons and initialize popovers
        const newInfoIcons = document.querySelectorAll('.info-icon[data-bs-toggle="popover"]');
        
        newInfoIcons.forEach(function(icon) {
            // Skip if already initialized
            if (icon.hasAttribute('data-popover-initialized')) {
                return;
            }
            
            // Mark as initialized
            icon.setAttribute('data-popover-initialized', 'true');
            
            // Create popover instance
            const popover = new bootstrap.Popover(icon, {
                html: true,
                trigger: 'manual',
                placement: 'right',
                fallbackPlacements: ['top', 'left', 'bottom'],
                container: 'body',
                sanitize: false,
                allowList: {
                    'strong': [],
                    'b': [],
                    'em': [],
                    'i': [],
                    'br': [],
                    'p': [],
                    'a': ['href', 'title', 'target'],
                    'div': ['class'],
                    'span': ['class']
                },
                delay: { show: 300, hide: 500 }
            });
            
            // Add hover event listeners
            let showTimeout, hideTimeout;
            
            icon.addEventListener('mouseenter', function() {
                clearTimeout(hideTimeout);
                showTimeout = setTimeout(function() {
                    popover.show();
                }, 300);
            });
            
            icon.addEventListener('mouseleave', function() {
                clearTimeout(showTimeout);
                hideTimeout = setTimeout(function() {
                    popover.hide();
                }, 500);
            });
            
            // Keep popover open when hovering over it
            icon.addEventListener('shown.bs.popover', function() {
                const popoverElement = document.querySelector('.popover');
                if (popoverElement) {
                    popoverElement.addEventListener('mouseenter', function() {
                        clearTimeout(hideTimeout);
                    });
                    
                    popoverElement.addEventListener('mouseleave', function() {
                        hideTimeout = setTimeout(function() {
                            popover.hide();
                        }, 500);
                    });
                }
            });
        });
    }
    
    // Flag to ensure we only run once
    let hasRun = false;

    // Function to attempt adding the popup
    function attemptAddPopup() {
        if (hasRun) return;

        const accordion2026 = document.querySelector('#price-year-title-2026');
        if (accordion2026) {
            hasRun = true;
            addInfoPopup();
            console.log('Info popup added to 2026 dates section');
        }
    }

    // MutationObserver to watch for dynamic content changes
    function setupMutationObserver() {
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    // Check if any added nodes contain our target element
                    for (let node of mutation.addedNodes) {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            if (node.querySelector && node.querySelector('#price-year-title-2026')) {
                                attemptAddPopup();
                                return;
                            }
                            if (node.id === 'price-year-title-2026') {
                                attemptAddPopup();
                                return;
                            }
                        }
                    }
                }
            });
        });

        // Start observing the dates/prices container
        const datesContainer = document.querySelector('#dates-pricing-accordion') || document.body;
        observer.observe(datesContainer, {
            childList: true,
            subtree: true
        });

        // Stop observing after 30 seconds to prevent memory leaks
        setTimeout(function() {
            observer.disconnect();
        }, 30000);
    }

    // Initialize on DOM ready
    function initialize() {
        // Try immediately in case content is already loaded
        attemptAddPopup();

        // Set up mutation observer for dynamic content
        if (!hasRun) {
            setupMutationObserver();
        }
    }

    // Start the process
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        initialize();
    }
    
})();
