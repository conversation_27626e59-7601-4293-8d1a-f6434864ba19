<?xml version="1.0" encoding="utf-8"?>
<extension version="2.5" type="plugin" group="easyblog" method="upgrade">
	<name>EasyBlog - Pagebreak</name>
	<author>Stack Ideas Sdn Bhd</author>
	<creationDate>25 April 2017</creationDate>
	<copyright>Copyright 2010 - 2017 StackIdeas. All rights reserved.</copyright>
	<license>GPL License</license>
	<authorEmail><EMAIL></authorEmail>
	<authorUrl>https://stackideas.com</authorUrl>
	<version>5.1.0</version>
	<description>This plugins will give you the ability to create pagination for your blog post. The HTML code is included here as a reference of what is available. The page break will be displayed in the text window as a simple horizontal line.</description>
	<languages>
		<language tag="en-GB">en-GB.plg_easyblog_pagebreak.ini</language>
	</languages>
	<files>
		<filename plugin="pagebreak">pagebreak.php</filename>
		<filename>index.html</filename>
	</files>
	<config>

		<fields name="params">
			<fieldset name="basic">
				<field name="noteUsage" type="note" class="alert" label="Usage" description="PLG_PAGEBREAK_USAGE" />
				<field
					name="enabled"
					type="list"
					default="1"
					label="Enable Plugin"
					description="PARAMENABLED" >
					<option value="0">No</option>
					<option value="1">Yes</option>
				</field>

				<field
					name="title"
					type="list"
					default="1"
					label="Site Title"
					description="PARAMSITETITLE" >
					<option value="0">No</option>
					<option value="1">Yes</option>
				</field>

				<field
					name="multipage_toc"
					type="list"
					default="1"
					label="Table of Contents"
					description="PARAMSITETOC" >
					<option value="0">No</option>
					<option value="1">Yes</option>
				</field>

				<field
					name="showall"
					type="list"
					default="1"
					label="Show All"
					description="PARAMSITESHOWALL" >
					<option value="0">No</option>
					<option value="1">Yes</option>
				</field>

				<field
					name="show_top_pagination"
					type="list"
					default="1"
					label="Show top pagination"
					description="Show pagebreak pagination on top" >
					<option value="0">No</option>
					<option value="1">Yes</option>
				</field>

				<field
					name="show_bot_pagination"
					type="list"
					default="1"
					label="Show bottom pagination"
					description="Show pagebreak pagination on bottom" >
					<option value="0">No</option>
					<option value="1">Yes</option>
				</field>
			</fieldset>
		</fields>
	</config>
	<updateservers>
		<server type="extension" priority="1" name="StackIdeas Modules and Plugins">https://stackideas.com/joomla4compat.xml</server>
	</updateservers>
</extension>