<?xml version="1.0" encoding="utf-8"?>
<extension version="2.5" type="plugin" group="easyblog" method="upgrade">
	<name>EasyBlog - Auto Article</name>
	<author>Stack Ideas Sdn Bhd</author>
	<creationDate>25 April 2017</creationDate>
	<copyright>Copyright 2010 - 2017 StackIdeas. All rights reserved.</copyright>
	<license>GPL License</license>
	<authorEmail><EMAIL></authorEmail>
	<authorUrl>https://stackideas.com</authorUrl>
	<version>5.1.0</version>
	<description>
		EasyBlog auto article creation
	</description>
	<files>
		<filename plugin="autoarticle">autoarticle.php</filename>
		<filename>index.html</filename>
		<folder>elements</folder>
	</files>
	<languages>
		<language tag="en-GB">en-GB.plg_easyblog_autoarticle.ini</language>
		<language tag="en-GB">en-GB.plg_easyblog_autoarticle.sys.ini</language>
	</languages>
	<config>
		<fields name="params">
			<fieldset name="basic" addfieldpath="/plugins/easyblog/autoarticle/elements/fields">
				<field
					name="status"
					type="list"
					default="0"
					label="PLG_EASYBLOG_AUTOARTICLE_ARTICLE_STATUS"
					description="PLG_EASYBLOG_AUTOARTICLE_ARTICLE_STATUS_DESC" >
					<option value="0">Unpublish</option>
					<option value="1">Publish</option>
				</field>

				<field
					name="access"
					type="list"
					default="-1"
					label="PLG_EASYBLOG_AUTOARTICLE_ARTICLE_PERMISSION"
					description="PLG_EASYBLOG_AUTOARTICLE_ARTICLE_PERMISSION_DESC" >
					<option value="-1"> Follow EasyBlog Permission</option>
					<option value="0"> Public </option>
					<option value="1"> Registered </option>
					<option value="2"> Special </option>
				</field>

				<field
					name="frontpage"
					type="list"
					default="1"
					label="PLG_EASYBLOG_AUTOARTICLE_ARTICLE_FRONTEND"
					description="PLG_EASYBLOG_AUTOARTICLE_ARTICLE_FRONTEND_DESC" >
					<option value="-1"> Follow EasyBlog Frontpage</option>
					<option value="1">JYES</option>
					<option value="0">JNO</option>
				</field>

				<field name="unpublish" type="list" default="0" label="PLG_EASYBLOG_AUTOARTICLE_ARTICLE_PUBLISH_AUTOMATICALLY" description="PLG_EASYBLOG_AUTOARTICLE_ARTICLE_PUBLISH_AUTOMATICALLY_DESC">
					<option value="1">JYES</option>
					<option value="0">JNO</option>
				</field>

				<field
					name="sectionCategory"
					type="modal_categories"
					default="0"
					class="form-select"
					label="PLG_EASYBLOG_AUTOARTICLE_ARTICLE_DEFAULT_CATEGORY"
					description="PLG_EASYBLOG_AUTOARTICLE_ARTICLE_DEFAULT_CATEGORY_DESC" />

				<field name="autocategory" type="list" default="0" label="PLG_EASYBLOG_AUTOARTICLE_ARTICLE_AUTO_MAP" description="PLG_EASYBLOG_AUTOARTICLE_ARTICLE_AUTO_MAP_DESC">
					<option value="1">JYES</option>
					<option value="0">JNO</option>
				</field>

				<field name="readmore" type="radio" class="btn-group" default="1" label="PLG_EASYBLOG_AUTOARTICLE_ARTICLE_SHOW_READMORE" description="PLG_EASYBLOG_AUTOARTICLE_ARTICLE_SHOW_READMORE_DESC">
					<option value="1">JYES</option>
					<option value="0">JNO</option>
				</field>
			</fieldset>
		</fields>
	</config>
	<updateservers>
		<server type="extension" priority="1" name="StackIdeas Modules and Plugins">https://stackideas.com/joomla4compat.xml</server>
	</updateservers>
</extension>
