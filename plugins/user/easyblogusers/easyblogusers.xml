<?xml version="1.0" encoding="utf-8"?>
<extension version="2.5" type="plugin" group="user" method="upgrade">
	<name>User - EasyBlog Users</name>
	<author>Stack Ideas Sdn Bhd</author>
	<creationDate>25 April 2017</creationDate>
	<copyright>Copyright 2010 - 2017 StackIdeas. All rights reserved.</copyright>
	<license>GPL License</license>
	<authorEmail><EMAIL></authorEmail>
	<authorUrl>https://stackideas.com</authorUrl>
	<version>5.1.0</version>
	<description>EasyBlog user plugin. This plugin is responsible to delete user's related records such as blog post and etc.</description>
	<files>
		<filename plugin="easyblogusers">easyblogusers.php</filename>
		<filename>index.html</filename>
		<folder>profiles</folder>
	</files>
	<languages>
		<language tag="en-GB">en-GB.plg_user_easyblogusers.ini</language>
		<language tag="en-GB">en-GB.plg_user_easyblogusers.sys.ini</language>
	</languages>
	<config>
		<fields name="params">
			<fieldset name="basic">
				<field name="show_subscribe" type="radio" class="btn-group" default="0" label="Show Subscribe To Blog In Profile" description="When this option is enabled, a new option will be added to their edit profile page in Joomla.">
					<option value="1">JYES</option>
					<option value="0">JNO</option>
				</field>
			</fieldset>
		</fields>
	</config>
	<updateservers>
		<server type="extension" priority="1" name="StackIdeas Modules and Plugins">https://stackideas.com/joomla4compat.xml</server>
	</updateservers>
</extension>